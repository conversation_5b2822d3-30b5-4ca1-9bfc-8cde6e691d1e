export enum DEFAULT_VALUE {
    IMAGE = '/assets/images/custom/default.png',
}

export const REACT_APP_API_URL = import.meta.env.VITE_REACT_APP_API_URL ?? '';
export const REACT_APP_AUTH_API_URL = import.meta.env.VITE_REACT_APP_AUTH_API_URL ?? REACT_APP_API_URL;
export const REACT_APP_PATH_CALLBACK = import.meta.env.VITE_PATH_CALLBACK ?? '';
export const FORM_FILE_ACCEPT = import.meta.env.VITE_FORM_FILE_ACCEPT ?? 'image/*,application/pdf';
export const FORM_MAX_RANGE = import.meta.env.VITE_FORM_MAX_RANGE ?? 140;

export const PAGINATION = {
    countItem: 0,
    totalPage: 1,
    currentPage: 1,
    limit: 10,
};

// export const optionConstantDefault: SelectOption = { value: 0, label: '' };
// export const optionModelDefault: SelectOptionModel = { value: '', label: '' };

export enum QUERY_KEY {
    ACTIONS = 'actions',
    USERS = 'users',
    USERS_LIST_ALL = 'users_list_all',
    USER = 'user',
    PROFILE = 'profile',
    //USER_ACTIONS = 'user_actions',
    GROUPS = 'groups',
    // USER_GROUPS = 'user_groups',
    // GROUP_ACTIONS = 'group_action',
    LOGOUT = 'logout',
    DEPARTMENTS = 'departments',
    GET_ALL_WORKFLOW = 'getAllWorkflows',
    WORKFLOWS = 'workflows',
    WORKFLOWS_DETAIL = 'WorkflowsDetail',
    WORKFLOWS_INSTANCE = 'WorkflowsInstance',
    WORKFLOWS_INSTANCE_DETAIL = 'WorkflowsInstanceDetail',
    USER_TASKS = 'userTasks',
    TASKS_DETAIL = 'tasksDetail',
    MICROSOFT_LOGIN_URL = 'microsoftLoginUrl',
    AREAS = 'areas',
    FORM_LIST = 'formList',
    FORM_DETAIL = 'formDetail',
    USER_ACCOUNT_LIST = 'userAccountList',
    USER_ACCOUNT_LIST_ALL = 'userAccountListAll',
    OMS_EMPLOYEE_INFO = 'omsEmployeeInfo',
    ROLES = 'roles',
    ROLE = 'role',
    ACTIVE_WORKFLOW_DETAIL = 'activeWorkflowDetail',
    WORKFLOW_DEFINITION_LIST_ALL = 'workflow_definition_list_all',
    AREAS_LIST_BY_SUB_AREA = 'area_list_by_sub_area',
    TASL_COMMENTS = 'task_comments',
    FILE_DETAIL = 'file_detail',
    USER_TASKS_DASHBOARD = 'user_tasks_dashboard',
    WORKFLOW_INSTANCE_DASHBOARD = 'workflow_instance_dashboard',
    CHANGE_REQUEST_DASHBOARD = 'change_request_dashboard',
    AUDIT_LOGS = 'audit_logs',
    AUDIT_LOG = 'audit_log',
    ERROR_LOGS = 'error_logs',
    ERROR_LOG = 'error_log',
    OVERVIEW_SUMMARY = 'overview_summary',
    OVERVIEW_TYPE_SUMMARY = 'overview_type_summary',
    OVERVIEW_AREA_SUMMARY = 'overview_area_summary',
    AUDIT_LOG_DASHBOARD = 'audit_log_dashboard',
    ERROR_LOG_DASHBOARD = 'error_log_dashboard',
    WORKFLOW_VERSIONS = 'workflow_versions',
    DASHBOARD_MOC_INSTANCES = 'dashboard_moc_workflow_instances',
    DASHBOARD_MOC_PROCESS_SUMMARY = 'dashboard_moc_process_summary',
    DASHBOARD_MOC_HEALTH_SUMMARY = 'dashboard_moc_health_summary',
    FORM_DASHBOARD = 'form_dashboard',
}

export const OPERATION_NAME = {
    LOGIN: 'login',
    LOGOUT: 'logout',
    ACTION_LIST: 'action_list',
    ACTION_CREATE: 'action_create',
    ACTION_DELETE: 'action_delete',
    FORGOT_PASSWORD: 'forgot_password',
    CHANGE_PASSWORD: 'change_password',
    REFRESH_TOKEN: 'refresh_token',
    REGISTER: 'register',
    PROFILE: 'profile',
    AUTH_PROFILE: 'auth_profile',
    MICROSOFT_LOGIN: 'auth_microsoftLogin',
    MICROSOFT_LOGIN_URL: 'auth_get_azure_login_url',
    MICROSOFT_LOGIN_WITH_CODE: 'auth_login_with_azure_code',
    MICROSOFT_LOGIN_WITH_CODE_PKCE: 'auth_login_with_azure_code_pkce',
} as const;

// Danh sách các operation name sử dụng AUTH API URL
export const AUTH_OPERATIONS = [
    OPERATION_NAME.LOGIN,
    OPERATION_NAME.LOGOUT,
    OPERATION_NAME.REGISTER,
    OPERATION_NAME.FORGOT_PASSWORD,
    OPERATION_NAME.CHANGE_PASSWORD,
    OPERATION_NAME.REFRESH_TOKEN,
    OPERATION_NAME.PROFILE,
    OPERATION_NAME.AUTH_PROFILE,
    OPERATION_NAME.ACTION_LIST,
];

export const FILTER_CONDITIONS = {
    EQUAL: '=',
    NOT_EQUAL: '!=',
    GREATER_THAN: '>',
    LESS_THAN: '<',
    GREATER_OR_EQUAL: '>=',
    LESS_OR_EQUAL: '<=',
    LIKE: '~',
    NOT_LIKE: '!~',
    IN: '[]',
    NOT_IN: '![]',
} as const;

export const PATH = {
    HOME: '/',
    NOT_FOUND: '/not-found',
} as const;

export const PAGE_NUMBER_DEFAULT = 1;
export const LIMIT_MAX = 999;

export enum COMMON_MESSAGE {
    ERROR_MESSAGE = 'An error occurred while processing your request',
    SUCCESS_MESSAGE = 'Update successfully',
    PERMISSION_DENY = 'You do not have permission to perform this action',
    EXPIRED_TOKEN = 'Your session has expired, please login again',
    FIELD_REQUIRED = 'This field is required',
    FIELD_PASSWORD_LENGTH = 'Use a password with a minimum length of 12 characters',
    FIELD_PASSWORD_MATCH = 'Passwords do not match',
    FIELD_EMAIL = 'Invalid email',
    FIELD_PHONE = 'Invalid phone number',
    FIELD_NUMBER = 'This field must be a number',
    MIN_NUMBER = 'The minimum value is ',
    MAX_NUMBER = 'The maximum value is ',
    FIELD_DATE = 'Invalid date',
    FIELD_REQUIRED_NAME = 'This field is required',
    FIELD_NUMBER_POSITIVE = 'The value must be greater than 0',
    DELETE_CONFIRM = 'Are you sure you want to delete this object?',
    ACTION_CONFIRM = 'Are you sure you want to perform this action?',
}

export const MIN_WIDTH = 10;
export const MAX_WIDTH = 100;
