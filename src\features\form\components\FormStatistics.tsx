import { useTranslation } from 'react-i18next';
import { FormDashboardQuery } from '../../../types/Form';

interface IProps {
    formDashboard?: FormDashboardQuery;
}

export default function FormStatistics({ formDashboard }: Readonly<IProps>) {
    const { t } = useTranslation();

    return (
        <div className="row">
            <div className="col-lg-3 col-sm-6 col-12">
                <div className="card">
                    <div className="card-header">
                        <div>
                            <h2 className="fw-bolder mb-0">{formDashboard?.form_dashboard?.total}</h2>
                            <p className="card-text">{t('Total Form')}</p>
                        </div>
                        <div className="avatar bg-light-primary p-50 m-0">
                            <div className="avatar-content">
                                <i className="font-medium-5 bi bi-list-task"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div className="col-lg-3 col-sm-6 col-12">
                <div className="card">
                    <div className="card-header">
                        <div>
                            <h2 className="fw-bolder mb-0">{formDashboard?.form_dashboard?.draft}</h2>
                            <p className="card-text">{t('Draft')}</p>
                        </div>
                        <div className="avatar bg-light-warning p-50 m-0">
                            <div className="avatar-content">
                                <i className="font-medium-5 bi bi-hourglass-split"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div className="col-lg-3 col-sm-6 col-12">
                <div className="card">
                    <div className="card-header">
                        <div>
                            <h2 className="fw-bolder mb-0">{formDashboard?.form_dashboard?.activated}</h2>
                            <p className="card-text">{t('Active')}</p>
                        </div>
                        <div className="avatar bg-light-success p-50 m-0">
                            <div className="avatar-content">
                                <i className="font-medium-5 bi bi-check-circle"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div className="col-lg-3 col-sm-6 col-12">
                <div className="card">
                    <div className="card-header">
                        <div>
                            <h2 className="fw-bolder mb-0">{formDashboard?.form_dashboard?.inactivated}</h2>
                            <p className="card-text">{t('Inactive')}</p>
                        </div>
                        <div className="avatar bg-light-danger p-50 m-0">
                            <div className="avatar-content">
                                <i className="font-medium-5 bi bi-check-circle"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
