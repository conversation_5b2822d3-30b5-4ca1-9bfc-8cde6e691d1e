import classNames from 'classnames';
import { Edit, Power, Trash2 } from 'react-feather';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { WorkflowDefinition, WorkflowStatus } from 'types/Workflow';
import { Paging } from 'types/common';
import { getStatusBadgeWorkflowList } from 'utils/common';

interface IProps {
    items: WorkflowDefinition[];
    paging: Paging;
    handleDelete?: (item: WorkflowDefinition) => void;
    handleChooseItem?: (item: WorkflowDefinition) => void;
    isSuperAdmin: boolean;
}

export default function ListWorkflow({ items, handleDelete, handleChooseItem, isSuperAdmin }: Readonly<IProps>) {
    const { t } = useTranslation();
    const navigate = useNavigate();

    const onEdit = (id: number) => {
        navigate(`/workflow/edit/${id}`);
    };

    return (
        <div className="table-responsive">
            <table className="table">
                <thead>
                    <tr>
                        <th className="text-center">version</th>
                        <th className="text-center">status</th>
                        <th>Name</th>
                        <th>actions</th>
                    </tr>
                </thead>
                <tbody>
                    {items?.map((item: WorkflowDefinition) => (
                        <tr key={item.id}>
                            <td className="text-center">{item.version}</td>
                            <td className="text-center">{getStatusBadgeWorkflowList(item.status)}</td>
                            <td>{item.name}</td>
                            <td className="d-flex tw-gap-[4px] tw-justify-start">
                                <button
                                    className="btn btn-icon btn-sm btn-flat-info waves-effect"
                                    title="Edit"
                                    onClick={() => onEdit(item?.id || 0)}
                                >
                                    <Edit size={14} />
                                </button>
                                {isSuperAdmin && (
                                    <>
                                        {item.status === WorkflowStatus.INACTIVE && (
                                            <>
                                                <button
                                                    className={classNames(
                                                        `btn btn-icon btn-sm btn-flat-success waves-effect`
                                                    )}
                                                    title={'Activate'}
                                                    onClick={() => handleChooseItem?.(item)}
                                                >
                                                    <Power size={14} />
                                                </button>
                                            </>
                                        )}

                                        {item.status === WorkflowStatus.DRAFT && (
                                            <>
                                                <button
                                                    type="button"
                                                    title="Delete"
                                                    className="btn btn-icon btn-sm btn-flat-danger waves-effect"
                                                    onClick={() => {
                                                        handleDelete?.(item);
                                                    }}
                                                >
                                                    <Trash2 size={14} />
                                                </button>
                                                <button
                                                    className={classNames(
                                                        `btn btn-icon btn-sm btn-flat-success waves-effect`
                                                    )}
                                                    title={'Activate'}
                                                    onClick={() => handleChooseItem?.(item)}
                                                >
                                                    <Power size={14} />
                                                </button>
                                            </>
                                        )}
                                    </>
                                )}
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
}
