import React, { useMemo } from 'react';
import Chart from 'react-apexcharts';
import { ApexOptions } from 'apexcharts';
import { ProcessStatusByStep } from '../../../../types/Dashboard';
import { getColumnWidth } from '../moc_overview/MOCAreaChart';

// Helper function để chuyển hex sang rgba
const hexToRgba = (hex: string, alpha: number) => {
    const r = parseInt(hex.slice(1, 3), 16);
    const g = parseInt(hex.slice(3, 5), 16);
    const b = parseInt(hex.slice(5, 7), 16);
    return `rgba(${r}, ${g}, ${b}, ${alpha})`;
};

interface MOCHealthBacklogProps {
    backlogByArea: ProcessStatusByStep[];
}

const MOCHealthBacklog = ({ backlogByArea }: Readonly<MOCHealthBacklogProps>) => {
    // Tính tổng count từ backlogByArea cho card Backlog
    const totalBacklogCount = useMemo(
        () => backlogByArea.reduce((total, area) => total + area.count, 0),
        [backlogByArea]
    );

    // Chuẩn bị data cho chart từ backlogByArea
    const chartCategories = useMemo(() => backlogByArea.map((area) => area.name), [backlogByArea]);

    const chartData = useMemo(() => backlogByArea.map((area) => area.count), [backlogByArea]);

    const dynamicBarHeight = getColumnWidth(chartCategories.length);

    // Chart options cho horizontal bar chart
    const chartOptions: ApexOptions = {
        chart: {
            type: 'bar',
            toolbar: {
                show: false,
            },
        },
        plotOptions: {
            bar: {
                horizontal: true,
                barHeight: dynamicBarHeight,
                distributed: false,
                dataLabels: {
                    position: 'center',
                },
            },
        },
        dataLabels: {
            enabled: true,
            style: {
                colors: ['#fff'],
                fontSize: '14px',
                fontWeight: 'bold',
            },
        },
        xaxis: {
            title: {
                text: 'Number of Backlog MOCs',
                style: {
                    fontSize: '12px',
                    fontWeight: 'bold',
                },
            },
            categories: chartCategories,
            labels: {
                style: {
                    fontSize: '12px',
                },
            },
        },
        yaxis: {
            labels: {
                style: {
                    fontSize: '10px',
                },
                maxWidth: 500,
            },
        },
        colors: ['#000000'], // Màu đen cho bars
        legend: {
            show: false,
        },
        grid: {
            show: true,
            borderColor: '#e9ecef',
        },
        title: {
            text: 'Backlog MOCs by Area (>90 Days Non-Movement)',
            align: 'center',
            style: {
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#373d3f',
            },
        },
        tooltip: {
            y: {
                formatter: function (val) {
                    return val.toString();
                },
            },
        },
        responsive: [
            {
                breakpoint: 768,
                options: {
                    yaxis: {
                        labels: {
                            style: {
                                fontSize: '8px',
                            },
                            maxWidth: 80,
                        },
                    },
                    xaxis: {
                        labels: {
                            style: {
                                fontSize: '10px',
                            },
                        },
                    },
                },
            },
        ],
    };

    const chartSeries = [
        {
            name: 'Backlog MOCs',
            data: chartData,
        },
    ];

    return (
        <div className="tw-mb-6">
            {/* Header với Backlog Card */}
            <div className="tw-flex tw-flex-wrap tw-items-center tw-justify-center tw-gap-4 tw-mb-4">
                {/* Backlog Card - màu đen */}
                <div className="tw-border-2 tw-bg-gray-50 tw-text-center tw-pb-3 tw-min-h-20 tw-justify-center tw-border-black">
                    <div className="tw-font-bold tw-text-sm tw-p-2 tw-text-white tw-bg-black/50">Backlog</div>
                    <div className="tw-text-2xl tw-font-bold tw-mt-2" style={{ color: '#000000' }}>
                        {totalBacklogCount}
                    </div>
                </div>
            </div>

            {/* Chart */}
            <div className="tw-mt-6">
                {backlogByArea.length > 0 ? (
                    <Chart
                        options={chartOptions}
                        series={chartSeries}
                        type="bar"
                        height={Math.max(400, backlogByArea.length * 25)}
                    />
                ) : (
                    <div className="tw-w-full tw-flex tw-justify-center tw-items-center tw-h-64">
                        <div className="tw-text-gray-500 tw-text-lg">No backlog data available</div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default MOCHealthBacklog;
