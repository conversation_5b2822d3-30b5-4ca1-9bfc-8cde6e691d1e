import SearchForm from 'components/partials/SearchForm';
import { useTranslation } from 'react-i18next';
import { convertStringConstantToSelectOptions } from 'utils/common';
import { SearchField } from '../../../types/common/Search';
import Area from '../../../types/OperationalArea';
import { TaskStatusNames } from 'types/Task';
import { TrackingStatusNames, workflowInstanceTypeNames } from '../../../types/Workflow';

interface IProps {
    isLoading: boolean;
    pageType: 'workflowInstance' | 'changeRequest';
    subArea?: Area[];
}

export default function SearchWorkflowInstanceForm({
    isLoading,
    pageType = 'workflowInstance',
    subArea,
}: Readonly<IProps>) {
    const { t } = useTranslation();
    let trackingStatusOption: { label: string; value: string }[] = TrackingStatusNames.map((item) => ({
        label: item.name,
        value: item.id,
    }));
    if (pageType === 'changeRequest') {
        trackingStatusOption = [{ label: 'Draft', value: 'draft' }, ...trackingStatusOption];
    }
    const subAreaOption: { label: string; value: string }[] =
        subArea?.map((item) => ({
            label: item.name,
            value: item.id,
        })) || [];

    const fields: SearchField[] = [
        {
            name: 'search',
            type: 'text',
            label: 'Search Request',
            wrapClassName: 'col-md-4 col-12',
            placeholder: 'Enter request title',
        },
        {
            name: 'type',
            type: 'select',
            label: 'Type',
            wrapClassName: 'col-md-4 col-12',
            options: {
                multiple: false,
                choices: workflowInstanceTypeNames.map((item) => ({
                    label: item.name,
                    value: item.id,
                })),
            },
        },
        {
            name: 'tracking_status',
            type: 'select',
            label: 'Tracking Status',
            wrapClassName: 'col-md-4 col-12',
            options: {
                multiple: true,
                choices: trackingStatusOption,
            },
        },
        {
            name: 'created_at__range',
            type: 'date-range',
            label: 'Creation Date Range',
            wrapClassName: 'col-md-4 col-12',
            options: {
                placeholderText: 'dd/MM/yyyy',
                dateFormat: 'dd/MM/yyyy',
                isClearable: true,
            },
        },
        {
            name: 'current_status',
            type: 'select',
            label: 'Current Status',
            wrapClassName: 'col-md-4 col-12',
            options: {
                multiple: true,
                choices: convertStringConstantToSelectOptions(
                    TaskStatusNames as { id: string; name: string }[],
                    t,
                    true
                ),
            },
        },
    ];

    if (pageType === 'workflowInstance') {
        fields.push({
            name: 'sub_area_id',
            type: 'select',
            label: 'Sub Area',
            wrapClassName: 'col-md-4 col-12',
            options: {
                multiple: true,
                choices: subAreaOption,
            },
        });
    }

    return <SearchForm fields={fields} isLoading={isLoading} />;
}
