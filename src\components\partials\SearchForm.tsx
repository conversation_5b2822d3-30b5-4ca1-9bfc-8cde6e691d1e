import classNames from 'classnames';
import ResetButton from 'components/partials/ResetButton';
import UpdateButton from 'components/partials/UpdateButton';
import { useEffect, useState } from 'react';
import { ArrowDown, ArrowUp } from 'react-feather';
import { Controller, FieldValues, useForm } from 'react-hook-form';
import { useLocation, useNavigate, useSearchParams } from 'react-router-dom';
import Select from 'react-select';
import { SearchField } from 'types/common/Search';
import { FORMAT_DATE, formatDateTime } from '../../utils/date';
import cn from 'utils/cn';
import { DateRangePicker as RsuiteDateRangePicker } from 'rsuite';
import 'rsuite/DateRangePicker/styles/index.css';
import { isDateValid } from '../../utils/common';

interface IProps {
    fields: SearchField[];
    isLoading: boolean;
    className?: string;
}

export default function SearchForm({ fields, isLoading, className }: Readonly<IProps>) {
    const [showSearch, setShowSearch] = useState(true);
    const { handleSubmit, reset, control } = useForm<FieldValues>({
        defaultValues: fields.reduce((acc, field) => {
            acc[field.name] = field.type === 'select' ? [] : '';
            return acc;
        }, {} as FieldValues),
    });
    const navigate = useNavigate();
    const location = useLocation();
    const [searchParams] = useSearchParams();
    const [newDateRangeValue, setNewDateRangeValue] = useState<[Date, Date] | null>(null);

    useEffect(() => {
        const defaultValues: FieldValues = {};
        fields.forEach((field) => {
            if (field.type === 'select') {
                const selectedValue = searchParams.get(field.name);
                if (selectedValue) {
                    const selectedValues = selectedValue.split(',');
                    defaultValues[field.name] = field.options.choices.filter((choice) =>
                        selectedValues.includes(choice.value.toString())
                    );
                } else {
                    defaultValues[field.name] = field.options.multiple ? [] : null;
                }
            } else if (field.type === 'date-range') {
                const dateRange = searchParams.get(field.name);
                if (dateRange) {
                    const [start, end] = dateRange.split('-');
                    setNewDateRangeValue([new Date(start), new Date(end)]);
                } else {
                    setNewDateRangeValue(null);
                }
                defaultValues[field.name] = dateRange || '';
            } else {
                defaultValues[field.name] = searchParams.get(field.name) ?? '';
            }
        });
        reset(defaultValues);
    }, [searchParams, fields, reset]);

    const onSubmit = (data: FieldValues) => {
        const newSearchParams = new URLSearchParams();
        Object.entries(data).forEach(([key, value]) => {
            if (value) {
                if (Array.isArray(value)) {
                    if (key.includes('.')) {
                        const keySplit = key.split('.');
                        if (data[keySplit[0]]) {
                            return;
                        }
                    }
                    const stringValue = value.map((item) => item.value).join(',');
                    if (stringValue) newSearchParams.set(key, stringValue);
                } else if (typeof value === 'object') {
                    if (value.value) {
                        newSearchParams.set(key, value.value);
                    } else {
                        Object.entries(value).forEach(([keyChild, valueChild]) => {
                            if (Array.isArray(valueChild)) {
                                const keySearch = `${key}.${keyChild}`;
                                const stringValue = valueChild.map((item) => item.value).join(',');
                                if (stringValue) newSearchParams.set(keySearch, stringValue);
                            }
                        });
                    }
                } else {
                    newSearchParams.set(key, value.toString());
                }
            }
        });
        navigate(`${location.pathname}?${newSearchParams.toString()}`);
    };

    const handleReset = () => {
        setNewDateRangeValue(null);
        reset();
        navigate(location.pathname);
    };

    return (
        <div className={cn('card', className)}>
            <div
                className="pb-1 cursor-pointer card-header d-flex justify-content-between align-items-start"
                onClick={() => setShowSearch((prev) => !prev)}
            >
                <h4 className="card-title">Search</h4>
                {!showSearch ? <ArrowDown size={14} /> : <ArrowUp size={14} />}
            </div>
            <div className={classNames('card-body', { 'd-none': !showSearch })}>
                <form className="form" onSubmit={handleSubmit(onSubmit)}>
                    <div className="row">
                        {fields.map((fieldItem) => {
                            // Nếu có customRender, sử dụng nó
                            if (fieldItem.customRender) {
                                return (
                                    <div className={fieldItem.wrapClassName} key={fieldItem.name}>
                                        {fieldItem.customRender(fieldItem)}
                                    </div>
                                );
                            }

                            // Nếu không có customRender, sử dụng render mặc định
                            switch (fieldItem.type) {
                                case 'number':
                                    return (
                                        <div className={fieldItem.wrapClassName} key={fieldItem.name}>
                                            <div className="mb-1">
                                                <div key={fieldItem.name}>
                                                    {fieldItem.label && (
                                                        <label htmlFor={fieldItem.name} className="form-label">
                                                            {fieldItem.label}
                                                        </label>
                                                    )}
                                                    <Controller
                                                        name={fieldItem.name}
                                                        control={control}
                                                        render={({ field }) => (
                                                            <input
                                                                type="number"
                                                                {...field}
                                                                className="form-control"
                                                                placeholder={fieldItem.placeholder}
                                                                step={1}
                                                            />
                                                        )}
                                                    />
                                                </div>
                                            </div>
                                        </div>
                                    );
                                case 'text':
                                    return (
                                        <div className={fieldItem.wrapClassName} key={fieldItem.name}>
                                            <div className="mb-1">
                                                <div key={fieldItem.name}>
                                                    {fieldItem.label && (
                                                        <label htmlFor={fieldItem.name} className="form-label">
                                                            {fieldItem.label}
                                                        </label>
                                                    )}
                                                    <Controller
                                                        name={fieldItem.name}
                                                        control={control}
                                                        render={({ field }) => (
                                                            <input
                                                                type="text"
                                                                {...field}
                                                                className="form-control"
                                                                placeholder={fieldItem.placeholder}
                                                                max={255}
                                                            />
                                                        )}
                                                    />
                                                </div>
                                            </div>
                                        </div>
                                    );
                                case 'select':
                                    return (
                                        <div className={fieldItem.wrapClassName} key={fieldItem.name}>
                                            <div className="mb-1">
                                                <div key={fieldItem.name}>
                                                    {fieldItem.label && (
                                                        <label htmlFor={fieldItem.name} className="form-label">
                                                            {fieldItem.label}
                                                        </label>
                                                    )}
                                                    <Controller
                                                        name={fieldItem.name}
                                                        control={control}
                                                        render={({ field }) => (
                                                            <Select
                                                                {...field}
                                                                options={fieldItem.options.choices}
                                                                isMulti={fieldItem.options.multiple}
                                                                onChange={(val) => field.onChange(val)}
                                                                filterOption={(option, inputValue) =>
                                                                    option.label
                                                                        .toLowerCase()
                                                                        .includes(inputValue.toLowerCase())
                                                                }
                                                                value={field.value}
                                                                isClearable
                                                                placeholder={fieldItem.placeholder}
                                                                styles={{
                                                                    control: (baseStyles, state) => ({
                                                                        ...baseStyles,
                                                                        borderColor: state.isFocused
                                                                            ? '#00AFF0'
                                                                            : baseStyles.borderColor,
                                                                        borderWidth: '1px',
                                                                        boxShadow: 'none',
                                                                        '&:hover': {
                                                                            borderColor: '#00AFF0',
                                                                        },
                                                                    }),
                                                                }}
                                                            />
                                                        )}
                                                    />
                                                </div>
                                            </div>
                                        </div>
                                    );
                                case 'date-range': {
                                    return (
                                        <div className={fieldItem.wrapClassName} key={fieldItem.name}>
                                            <div className="mb-1">
                                                <div key={fieldItem.name}>
                                                    {fieldItem.label && (
                                                        <label htmlFor={fieldItem.name} className="form-label">
                                                            {fieldItem.label}
                                                        </label>
                                                    )}
                                                    <Controller
                                                        name={fieldItem.name}
                                                        control={control}
                                                        render={({ field: { ...fieldProps } }) => (
                                                            <RsuiteDateRangePicker
                                                                format="dd/MM/yyyy"
                                                                character=" – "
                                                                className="form-control new-date-range-picker"
                                                                placeholder="dd/mm/yyyy - dd/mm/yyyy"
                                                                onOk={(value) => {
                                                                    const startDate =
                                                                        Array.isArray(value) &&
                                                                        value[0] &&
                                                                        isDateValid(value[0])
                                                                            ? formatDateTime(
                                                                                  value[0],
                                                                                  FORMAT_DATE.DB_DATE_2
                                                                              )
                                                                            : '';
                                                                    const endDate =
                                                                        Array.isArray(value) &&
                                                                        value[1] &&
                                                                        isDateValid(value[1])
                                                                            ? formatDateTime(
                                                                                  value[1],
                                                                                  FORMAT_DATE.DB_DATE_2
                                                                              )
                                                                            : '';
                                                                    fieldProps.onChange(`${startDate}-${endDate}`);
                                                                    setNewDateRangeValue(value);
                                                                }}
                                                                onChange={(value) => {
                                                                    if (value) {
                                                                        const startDate =
                                                                            Array.isArray(value) &&
                                                                            value[0] &&
                                                                            isDateValid(value[0])
                                                                                ? formatDateTime(
                                                                                      value[0],
                                                                                      FORMAT_DATE.DB_DATE_2
                                                                                  )
                                                                                : '';
                                                                        const endDate =
                                                                            Array.isArray(value) &&
                                                                            value[1] &&
                                                                            isDateValid(value[1])
                                                                                ? formatDateTime(
                                                                                      value[1],
                                                                                      FORMAT_DATE.DB_DATE_2
                                                                                  )
                                                                                : '';
                                                                        if (startDate && endDate)
                                                                            fieldProps.onChange(
                                                                                `${startDate}-${endDate}`
                                                                            );
                                                                        if (value.length === 2)
                                                                            setNewDateRangeValue(value);
                                                                    }
                                                                }}
                                                                onClean={() => {
                                                                    setNewDateRangeValue(null);
                                                                    fieldProps.onChange('');
                                                                }}
                                                                value={newDateRangeValue}
                                                            />
                                                        )}
                                                    />
                                                </div>
                                            </div>
                                        </div>
                                    );
                                }
                                // Other fields ...
                                default:
                                    return null;
                            }
                        })}
                        <div className="col-12">
                            <UpdateButton
                                btnText="Search"
                                isLoading={isLoading}
                                hasDivWrap={false}
                                btnClass={['me-1']}
                            />
                            <ResetButton btnText="Reset" isLoading={isLoading} handleReset={handleReset} />
                        </div>
                    </div>
                </form>
            </div>
        </div>
    );
}
