import { Helmet } from 'react-helmet-async';
import { useAuthStore } from '../../../stores/authStore';
import { ChangeEvent, useMemo, useState } from 'react';
import { AuthGroups } from '../../../types/User';
import ContentHeader from '../../../components/partials/ContentHeader';
import { generateFilters, showToast } from '../../../utils/common';
import { AuditLogStatistical, AuditLogSearchForm, ListAuditLogs, AuditLogDetailModal } from '../components/auditLog';
import PaginationTable from '../../../components/partials/PaginationTable';
import useQueryParams from '../../../hooks/useQueryParams';
import omitBy from 'lodash/omitBy';
import { PAGINATION, QUERY_KEY } from '../../../constants/common';
import { convertDateRangeToQueryParams } from '../../../utils/date';
import isUndefined from 'lodash/isUndefined';
import {
    AuditLog,
    AuditLogDashboard,
    auditLogFilterConfig,
    AuditLogQuery,
    SearchAuditLogParam,
} from '../../../types/Logs';
import { useGraphQLQuery } from '../../../hooks/useGraphQLQuery';
import { BaseSearch } from '../../../types/common';
import { AUDIT_LOG_DASHBOARD, AUDIT_LOG_DETAIL, AUDIT_LOG_LIST, exportAuditLog } from '../../../services/LogService';
import { keepPreviousData } from '@tanstack/react-query';
import { HttpStatusCode } from 'axios';
import Spinner from '../../../components/partials/Spinner';

export default function UserList() {
    const user = useAuthStore((state) => state.user);
    const isSuperAdmin = useMemo(() => user?.auth_group === AuthGroups.SUPER_ADMIN, [user?.auth_group]);

    // Modal state
    const [showDetailModal, setShowDetailModal] = useState(false);
    const [selectedLogId, setSelectedLogId] = useState('');

    const handleExport = async () => {
        const res = await exportAuditLog({ search, filters, sort: paramConfig.sort });
        if (res.status === HttpStatusCode.Created) {
            showToast(true, ['Export successfully']);
            const blob = new Blob([res.data], { type: res.headers['content-type'] });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `audit-logs-${new Date().toISOString()}.xlsx`;
            a.click();
            window.URL.revokeObjectURL(url);
        }
    };

    const handleViewDetail = (logId: string) => {
        setSelectedLogId(logId);
        setShowDetailModal(true);
    };

    const { queryParams, setQueryParams } = useQueryParams<SearchAuditLogParam>();
    const paramConfig: SearchAuditLogParam = omitBy(
        {
            limit: queryParams.limit ?? PAGINATION.limit,
            page: queryParams.page ?? '1',
            search: queryParams.search,
            created_at_from:
                convertDateRangeToQueryParams(queryParams.created_at__range || '')?.created_at_from ?? undefined,
            created_at_to:
                convertDateRangeToQueryParams(queryParams.created_at__range || '')?.created_at_to ?? undefined,
            action_type: queryParams.action_type,
            sort: queryParams.sort,
        },
        isUndefined
    );

    const { limit, search, page, ...dataParamConfig } = paramConfig;
    const filters = generateFilters(dataParamConfig, auditLogFilterConfig);

    const { data, isLoading, isRefetching } = useGraphQLQuery<AuditLogQuery, BaseSearch>(
        [QUERY_KEY.AUDIT_LOGS, queryParams],
        AUDIT_LOG_LIST,
        {
            page: Number(page),
            limit: Number(limit),
            search: search || undefined,
            filters: filters.length > 0 ? filters : undefined,
            sort: undefined,
        },
        '',
        {
            placeholderData: keepPreviousData,
        }
    );
    const auditLogs = useMemo(() => data?.audit_log_list?.data ?? [], [data]);

    const handlePageChange = (_event: ChangeEvent<unknown>, page: number) => {
        setQueryParams({ ...queryParams, page: page.toString() });
    };

    const { data: auditLogDetailData } = useGraphQLQuery<{ audit_log_detail: AuditLog }>(
        [QUERY_KEY.AUDIT_LOG, selectedLogId],
        AUDIT_LOG_DETAIL,
        { id: selectedLogId },
        '',
        {
            enabled: !!selectedLogId,
            placeholderData: keepPreviousData,
        }
    );
    const auditLogDetail = useMemo(() => auditLogDetailData?.audit_log_detail, [auditLogDetailData]);

    const { data: auditLogDashboardData } = useGraphQLQuery<{ audit_log_dashboard: AuditLogDashboard }>(
        [QUERY_KEY.AUDIT_LOG_DASHBOARD],
        AUDIT_LOG_DASHBOARD,
        undefined,
        '',
        {
            placeholderData: keepPreviousData,
        }
    );
    const auditLogDashboard = useMemo(() => auditLogDashboardData?.audit_log_dashboard, [auditLogDashboardData]);

    return (
        <>
            <Helmet>
                <title>{'Audit Log'}</title>
            </Helmet>
            <ContentHeader
                title={'Audit Log'}
                contextMenu={
                    isSuperAdmin
                        ? [
                              {
                                  text: 'Export To Excel',
                                  to: ``,
                                  icon: 'DOWNLOAD',
                                  fnCallBack: { actionMenu: handleExport },
                              },
                          ]
                        : []
                }
            />
            <div className="content-body">
                <div className="row">
                    <div className="col-4">
                        <AuditLogStatistical auditLogDashboard={auditLogDashboard} />
                    </div>
                    <div className="col-12">
                        <AuditLogSearchForm isLoading={isLoading} isRefetching={isRefetching} />
                    </div>
                    <div className="col-12">
                        <div className="card">
                            {isLoading || (isRefetching && <Spinner />)}
                            {!isLoading && !isRefetching && (
                                <>
                                    <ListAuditLogs onViewDetail={handleViewDetail} auditLogs={auditLogs} />
                                    <PaginationTable
                                        countItem={data?.audit_log_list?.totalCount || 0}
                                        totalPage={data?.audit_log_list?.totalPages || 1}
                                        currentPage={data?.audit_log_list?.currentPage || 1}
                                        handlePageChange={handlePageChange}
                                    />
                                </>
                            )}
                        </div>
                    </div>
                </div>
            </div>

            {/* Audit Log Detail Modal */}
            <AuditLogDetailModal show={showDetailModal} changeShow={setShowDetailModal} auditLog={auditLogDetail} />
        </>
    );
}
