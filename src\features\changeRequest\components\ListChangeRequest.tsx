import { <PERSON>, Eye } from 'react-feather';
import { useNavigate, Link } from 'react-router-dom';
import { WorkflowInstance } from 'types/Workflow';
import { FORMAT_DATE, formatDateTime } from '../../../utils/date';
import { getStatusBadgeStep, getStatusTypeChangeRequest, getTrackingStatusBadge } from '../../../utils/common';

interface IProps {
    items: WorkflowInstance[];
}

export default function ListChangeRequest({ items }: Readonly<IProps>) {
    const navigate = useNavigate();

    const onView = (id: string) => {
        navigate(`/changeRequest/view/${id}`);
    };
    const onEdit = (id: string) => {
        navigate(`/changeRequest/edit/${id}`);
    };

    return (
        <div className="table-responsive">
            <table className="table">
                <thead>
                    <tr>
                        <th className="!tw-px-4">Request No</th>
                        <th className="!tw-px-4">Request Title</th>
                        <th className="!tw-px-4">type</th>
                        <th className="!tw-px-4">Current step</th>
                        <th className="text-center !tw-px-4">Current Status</th>
                        <th className="text-center !tw-px-4">Tracking status</th>
                        <th className="!tw-px-4">Creation Date</th>
                        <th className="!tw-px-4">Last Updated</th>
                        <th className="text-center !tw-px-4">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {items?.map((item: WorkflowInstance) => (
                        <tr key={item?.id}>
                            <td className="!tw-px-4">
                                <Link
                                    to={
                                        item?.status === 'DRAFT'
                                            ? `/changeRequest/edit/${item?.id}`
                                            : `/changeRequest/view/${item?.id}`
                                    }
                                    className="text-primary"
                                >
                                    {item?.business_key}
                                </Link>
                            </td>
                            <td className="!tw-px-4">{item?.name}</td>
                            <td className="!tw-px-4">{getStatusTypeChangeRequest(item?.type)}</td>
                            <td className="!tw-px-4">{item?.current_user_task?.workflow_step?.step_name}</td>
                            <td className="text-center !tw-px-4">
                                {getStatusBadgeStep(item?.current_user_task?.status)}
                            </td>
                            <td className="text-center !tw-px-4">{getTrackingStatusBadge(item?.tracking_status)}</td>
                            <td className="!tw-px-4">
                                {formatDateTime(item?.created_at, FORMAT_DATE.SHOW_DATE_MINUTE)}
                            </td>
                            <td className="!tw-px-4">
                                {formatDateTime(item?.updated_at, FORMAT_DATE.SHOW_DATE_MINUTE)}
                            </td>
                            <td className="text-center !tw-px-4">
                                {item?.status === 'DRAFT' ? (
                                    <button
                                        className="btn btn-icon btn-sm btn-flat-info waves-effect"
                                        title="Edit"
                                        onClick={() => onEdit(item?.id)}
                                    >
                                        <Edit size={14} />
                                    </button>
                                ) : (
                                    <button
                                        className="btn btn-icon btn-sm btn-flat-info waves-effect"
                                        title="Edit"
                                        onClick={() => onView(item?.id)}
                                    >
                                        <Eye size={14} />
                                    </button>
                                )}
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
}
