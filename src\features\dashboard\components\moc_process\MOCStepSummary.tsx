import React from 'react';
import { ProcessStatusByStep } from '../../../../types/Dashboard';

interface IProps {
    statusByStep: ProcessStatusByStep[];
}

// Helper function để chuyển hex sang rgba
const hexToRgba = (hex: string, alpha: number) => {
    const r = parseInt(hex.slice(1, 3), 16);
    const g = parseInt(hex.slice(3, 5), 16);
    const b = parseInt(hex.slice(5, 7), 16);
    return `rgba(${r}, ${g}, ${b}, ${alpha})`;
};

// Mảng 15 màu: 9 màu từ ảnh + 6 màu bổ sung
export const STEP_COLORS = [
    '#007bff', // In Progress - xanh dương
    '#28a745', // Step 1 - xanh lá
    '#17a2b8', // Step 2 - xanh cyan
    '#ffc107', // Step 3 - vàng
    '#6f42c1', // Step 4 - tím
    '#20c997', // Step 5 - xanh mint
    '#dc3545', // Step 6 - đỏ
    '#fd7e14', // Step 7 - cam
    '#6c757d', // Step 8 - xám
    '#e83e8c', // Step 9 - hồng
    '#795548', // Màu nâu
    '#607d8b', // Màu xanh xám
    '#ff5722', // Màu cam đậm
    '#9c27b0', // Màu tím đậm
    '#00bcd4', // Màu xanh nhạt
];

const MOCStepSummary = ({ statusByStep }: Readonly<IProps>) => {
    const totalCount = statusByStep.reduce((total, step) => total + step.count, 0);

    return (
        <div className="tw-mb-6">
            <h4 className="tw-text-gray-600 tw-font-bold tw-text-xl tw-mb-4 tw-text-center">Status By Step</h4>

            {/* Header với In Progress và các Steps */}
            <div className="tw-flex tw-flex-wrap tw-items-center tw-justify-center tw-gap-4 tw-mb-4">
                {/* In Progress */}
                <div
                    className="tw-border-2 tw-bg-gray-50 tw-text-center tw-pb-3 tw-min-h-20 tw-justify-center"
                    style={{ borderColor: STEP_COLORS[0], width: '100px' }}
                    title="In Progress"
                >
                    <div
                        className="tw-font-bold tw-text-sm tw-p-2 tw-text-white tw-truncate tw-px-1"
                        style={{ backgroundColor: hexToRgba(STEP_COLORS[0], 0.5) }}
                        title="In Progress"
                    >
                        In Progress
                    </div>
                    <div className="tw-text-2xl tw-font-bold tw-mt-2" style={{ color: STEP_COLORS[0] }}>
                        {totalCount}
                    </div>
                </div>

                {/* Các Steps */}
                {statusByStep.map((step, index) => {
                    // Sử dụng index + 1 để lấy màu từ STEP_COLORS (bỏ qua index 0 dành cho In Progress)
                    const colorIndex = (index + 1) % STEP_COLORS.length;
                    const stepColor = STEP_COLORS[colorIndex];

                    return (
                        <div
                            key={step.id}
                            className="tw-border-2 tw-bg-gray-50 tw-text-center tw-pb-3 tw-min-h-20 tw-justify-center"
                            style={{ borderColor: stepColor, width: '100px' }}
                            title={step.name}
                        >
                            <div
                                className="tw-font-bold tw-text-sm tw-py-2 tw-text-white tw-truncate tw-px-1"
                                style={{ backgroundColor: hexToRgba(stepColor, 0.5) }}
                                title={step.name}
                            >
                                {step.name}
                            </div>
                            <div className="tw-text-2xl tw-font-bold tw-mt-2" style={{ color: stepColor }}>
                                {step.count}
                            </div>
                        </div>
                    );
                })}
            </div>
        </div>
    );
};

export default MOCStepSummary;
