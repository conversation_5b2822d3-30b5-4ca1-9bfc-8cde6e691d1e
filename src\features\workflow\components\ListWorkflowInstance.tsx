import { Eye } from 'react-feather';
import { WorkflowInstance } from '../../../types/Workflow';
import { useNavigate, Link } from 'react-router-dom';
import { FORMAT_DATE, formatDateTime } from '../../../utils/date';
import { getStatusBadgeStep, getStatusTypeChangeRequest, getTrackingStatusBadge } from 'utils/common';
import Area from '../../../types/OperationalArea';

interface IProps {
    items: WorkflowInstance[];
    areas_list: Area[];
    onSort?: (field: string) => void;
    currentSort?: string;
}

export default function ListWorkflowInstance({ items, areas_list }: Readonly<IProps>) {
    const navigate = useNavigate();

    const onView = (id: string) => {
        navigate(`/workflowInstance/${id}`);
    };

    const getAreaNames = (subAreaIds: string): string => {
        if (!subAreaIds || !areas_list) return '';

        const areaIds = subAreaIds
            .split(',')
            .map((id) => id.trim())
            .filter((id) => id);
        return areaIds
            .map((id) => areas_list.find((area) => area.id === id)?.name)
            .filter((name) => name)
            .join(', ');
    };

    return (
        <div className="table-responsive">
            <table className="table">
                <thead>
                    <tr>
                        <th className="!tw-px-4">MOC No</th>
                        <th className="!tw-px-4">Request Title</th>
                        <th className="!tw-px-4">WORKFLOW</th>
                        <th className="!tw-px-4">type</th>
                        <th className="!tw-px-4">area</th>
                        <th className="!tw-px-4">Current step</th>
                        <th className="text-center !tw-px-4">Current Status</th>
                        <th className="text-center !tw-px-4">Tracking status</th>
                        <th className="text-center !tw-px-4">Originator</th>
                        <th className="!tw-px-4">Creation Date</th>
                        <th className="!tw-px-4">Last Updated</th>
                        <th className="text-center !tw-px-4">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {items?.map((item: WorkflowInstance) => (
                        <tr key={item?.id}>
                            <td className="!tw-px-4">
                                <Link to={`/workflowInstance/${item?.id}`} className="text-primary">
                                    {item?.business_key}
                                </Link>
                            </td>
                            <td className="!tw-px-4">{item?.name}</td>
                            <td className="!tw-px-4">
                                {item?.workflow_definition?.name + `(v${item?.workflow_definition?.version})`}
                            </td>
                            <td className="!tw-px-4">{getStatusTypeChangeRequest(item?.type)}</td>
                            <td className="!tw-px-4">{getAreaNames(item?.sub_area_ids || '')}</td>
                            <td className="!tw-px-4">{item?.current_user_task?.workflow_step?.step_name}</td>
                            <td className="text-center !tw-px-4">
                                {getStatusBadgeStep(item?.current_user_task?.status)}
                            </td>
                            <td className={'text-center !tw-px-4'}>{getTrackingStatusBadge(item?.tracking_status)}</td>
                            <td className="text-center !tw-px-4">{item?.creator?.full_name}</td>
                            <td className="!tw-px-4">
                                {formatDateTime(item?.created_at, FORMAT_DATE.SHOW_DATE_MINUTE)}
                            </td>
                            <td className="!tw-px-4">
                                {formatDateTime(item?.updated_at, FORMAT_DATE.SHOW_DATE_MINUTE)}
                            </td>
                            <td className="text-center !tw-px-4">
                                <button
                                    className="btn btn-icon btn-sm btn-flat-info waves-effect"
                                    title="View"
                                    onClick={() => onView(item?.id)}
                                >
                                    <Eye size={14} />
                                </button>
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
}
