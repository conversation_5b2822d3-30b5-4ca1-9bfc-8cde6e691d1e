import { gql } from 'graphql-request';
import Http from './http.class';
const httpInstance = new Http().instance;

export const AUDIT_LOG_LIST = gql`
    query Audit_log_list($page: Int!, $limit: Int!, $search: String, $filters: [String!], $sort: String) {
        audit_log_list(filter: { search: $search, filters: $filters, sort: $sort, page: $page, limit: $limit }) {
            totalCount
            totalPages
            currentPage
            data {
                id
                role_id
                action_type
                moc_request_id
                workflow_step_id
                description
                ip_address
                user_agent
                workflow_definition_id
                updated_user_id
                user_task_id
                created_at
                userTask {
                    workflow_step {
                        step_name
                        id
                    }
                    id
                    userRoles {
                        id
                        name
                    }
                }
                creator {
                    id
                    full_name
                    oms_emp_code
                }
                operation_area_ids
                role {
                    id
                    name
                }
                mocRequest {
                    name
                }
                workflow_step {
                    step_name
                    id
                }
            }
        }
    }
`;

export const AUDIT_LOG_DETAIL = gql`
    query Audit_log_detail($id: String!) {
        audit_log_detail(id: $id) {
            id
            role_id
            action_type
            moc_request_id
            workflow_step_id
            description
            ip_address
            user_agent
            workflow_definition_id
            updated_user_id
            user_task_id
            created_at
            userTask {
                workflow_step {
                    step_name
                    id
                }
                id
                userRoles {
                    id
                    name
                }
            }
            creator {
                id
                full_name
                oms_emp_code
            }
            operation_area_ids
            role {
                id
                name
            }
            mocRequest {
                id
                status
                name
            }
            workflow_step {
                step_name
                id
            }
        }
    }
`;

export const ERROR_LOGS_LIST = gql`
    query Error_logs_list($page: Int!, $limit: Int!, $search: String, $filters: [String!], $sort: String) {
        error_logs_list(options: { search: $search, filters: $filters, sort: $sort, page: $page, limit: $limit }) {
            totalCount
            totalPages
            currentPage
            data {
                id
                created_at
                error_category
                api_endpoint
                error_message
                user_id
                ip_address
                user_agent
                user_task_id
                form_id
                workflow_instance_id
                workflow_step_id
                workflow_definition_id
                operation_area_ids
                role_ids
                system
            }
        }
    }
`;

export const AUDIT_LOG_DASHBOARD = gql`
    query Audit_log_dashboard {
        audit_log_dashboard {
            total
        }
    }
`;

export const ERROR_LOG_DASHBOARD = gql`
    query Error_log_dashboard {
        error_log_dashboard {
            total
            oms
            system
        }
    }
`;

export const ERROR_LOG_DETAIL = gql`
    query Error_log_detail($id: String!) {
        error_log_detail(id: $id) {
            id
            created_at
            error_category
            api_endpoint
            error_message
            user_id
            ip_address
            user_agent
            user_task_id
            form_id
            workflow_instance_id
            workflow_step_id
            workflow_definition_id
            operation_area_ids
            submit_value
            role_ids
            created_by
            form {
                id
                name
            }
            user {
                id
                full_name
            }
            user_task {
                id
                task_id
                task_key
                task_name
                workflow_instance {
                    id
                    name
                }
            }
            workflow_instance {
                camunda_key
                business_key
                name
                status
            }
            workflow_step {
                id
                workflow_definition_id
                step_key
                step_name
                step_order
                standar_time
            }
            workflow_definition {
                id
                name
                bpmnXml
                status
                camunda_key
                camunda_id
                version
            }
            roles {
                id
                user_id
                area_id
                role_id
                create_at
                role {
                    id
                    name
                    type
                }
            }
            operationAreas {
                id
                type
                name
                code
                out_of_service
                parent_area_id
            }
            creator {
                id
                full_name
            }
        }
    }
`;

export const exportAuditLog = (body: Readonly<{ search?: string; filters?: string[]; sort?: string }>) =>
    httpInstance.post('/audit-logs/export', body, {
        responseType: 'blob',
    });
export const exportErrorLog = (body: Readonly<{ search?: string; filters?: string[]; sort?: string }>) =>
    httpInstance.post('/error-logs/export', body, {
        responseType: 'blob',
    });
