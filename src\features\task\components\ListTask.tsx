import { Task, TaskStatus } from '../../../types/Task';
import { FORMAT_DATE, formatDateTime } from 'utils/date';
import { Link } from 'react-router-dom';
import { Edit, Eye } from 'react-feather';
import { getStatusBadgeStep, getTrackingStatusBadge } from 'utils/common';

interface IProps {
    items: Task[];
}

export default function ListTask({ items }: Readonly<IProps>) {
    return (
        <div className="table-responsive">
            <table className="table">
                <thead>
                    <tr>
                        <th className="text-center">Task No</th>
                        <th>Task Name</th>
                        <th>Request Title</th>
                        <th>Request No</th>
                        <th className="text-center">Task Status</th>
                        <th className="text-center">Tracking Status</th>
                        <th>Creation Date</th>
                        <th>Last Updated</th>
                        <th className="text-center">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {items.map((item: Task) => (
                        <tr key={item.id}>
                            <td className="text-center">{item?.task_no}</td>
                            <td>{item?.task_name}</td>
                            <td>{item.workflow_instance?.name}</td>
                            <td>{item.workflow_instance?.business_key}</td>
                            <td className="text-center">{getStatusBadgeStep(item?.status)}</td>
                            <td className="text-center">{getTrackingStatusBadge(item?.tracking_status)}</td>
                            <td>
                                {item.created_at ? formatDateTime(item?.created_at, FORMAT_DATE.SHOW_DATE_MINUTE) : ''}
                            </td>
                            <td>
                                {item.updated_at ? formatDateTime(item?.updated_at, FORMAT_DATE.SHOW_DATE_MINUTE) : ''}
                            </td>
                            <td className="text-center">
                                {item.status === TaskStatus.IN_PROGRESS ? (
                                    <Link to={`/task/edit/${item.id}`}
                                        className="btn btn-icon btn-sm btn-flat-info waves-effect"
                                        title="Edit"
                                    >
                                        <Edit size={14} />
                                    </Link>
                                ) : (
                                    <Link to={`/task/view/${item.id}`}
                                        className="btn btn-icon btn-sm btn-flat-info waves-effect"
                                        title="View"
                                    >
                                        <Eye size={14} />
                                    </Link>
                                )}
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
}
