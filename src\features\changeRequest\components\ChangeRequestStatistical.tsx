import React, { useMemo } from 'react';
import { ChangeRequestDashboard, TrackingStatus } from '../../../types/Workflow';

interface IProps {
    changeRequestDashboard?: ChangeRequestDashboard;
    onChangeTrackingStatus: (trackingStatus: string | null) => void;
    type?: 'workflowInstance' | 'changeRequest';
}

export default function ChangeRequestStatistical({
    changeRequestDashboard,
    onChangeTrackingStatus,
    type,
}: Readonly<IProps>) {
    // Tính tổng các status
    const totalCount = useMemo(() => {
        if (!changeRequestDashboard) return 0;

        return (
            (changeRequestDashboard.in_progress || 0) +
            (changeRequestDashboard.completed || 0) +
            (changeRequestDashboard.cancelled || 0) +
            (changeRequestDashboard.overdue || 0) +
            (changeRequestDashboard.backlog || 0)
        );
    }, [changeRequestDashboard]);
    return (
        <div className="tw-grid tw-grid-cols-1 sm:tw-grid-cols-2 lg:tw-grid-cols-3 xl:tw-grid-cols-6 tw-gap-4 tw-w-full">
            {type === 'workflowInstance' && (
                <button
                    type="button"
                    className="card tw-w-full tw-text-left"
                    onClick={() => onChangeTrackingStatus(null)}
                >
                    <div className="card-header">
                        <div>
                            <h2 className="fw-bolder mb-0">{totalCount}</h2>
                            <p className="card-text">Total</p>
                        </div>
                        <div className="avatar bg-light-info p-50 m-0">
                            <div className="avatar-content">
                                <i className="font-medium-5 bi bi-list-task"></i>
                            </div>
                        </div>
                    </div>
                </button>
            )}
            {type === 'changeRequest' && (
                <button
                    type="button"
                    className="card tw-w-full tw-text-left"
                    onClick={() => onChangeTrackingStatus(TrackingStatus.DRAFT)}
                >
                    <div className="card-header">
                        <div>
                            <h2 className="fw-bolder mb-0">{changeRequestDashboard?.draft}</h2>
                            <p className="card-text">Draft</p>
                        </div>
                        <div className="avatar bg-light-default p-50 m-0">
                            <div className="avatar-content">
                                <i className="font-medium-5 bi bi-list-task"></i>
                            </div>
                        </div>
                    </div>
                </button>
            )}
            <button
                type="button"
                className="card tw-w-full tw-text-left"
                onClick={() => onChangeTrackingStatus(TrackingStatus.ON_TRACKING)}
            >
                <div className="card-header">
                    <div>
                        <h2 className="fw-bolder mb-0">{changeRequestDashboard?.in_progress}</h2>
                        <p className="card-text">On Tracking</p>
                    </div>
                    <div className="avatar bg-light-warning p-50 m-0">
                        <div className="avatar-content">
                            <i className="font-medium-5 bi bi-list-task"></i>
                        </div>
                    </div>
                </div>
            </button>
            <button
                type="button"
                className="card tw-w-full tw-text-left"
                onClick={() => onChangeTrackingStatus(TrackingStatus.COMPLETED)}
            >
                <div className="card-header">
                    <div>
                        <h2 className="fw-bolder mb-0">{changeRequestDashboard?.completed}</h2>
                        <p className="card-text">Completed</p>
                    </div>
                    <div className="avatar bg-light-success p-50 m-0">
                        <div className="avatar-content">
                            <i className="font-medium-5 bi bi-hourglass-split"></i>
                        </div>
                    </div>
                </div>
            </button>
            <button
                type="button"
                className="card tw-w-full tw-text-left"
                onClick={() => onChangeTrackingStatus(TrackingStatus.CANCELLED)}
            >
                <div className="card-header">
                    <div>
                        <h2 className="fw-bolder mb-0">{changeRequestDashboard?.cancelled}</h2>
                        <p className="card-text">Cancelled</p>
                    </div>
                    <div className="avatar bg-light-danger p-50 m-0">
                        <div className="avatar-content">
                            <i className="font-medium-5 bi bi-check-circle"></i>
                        </div>
                    </div>
                </div>
            </button>
            <button
                type="button"
                className="card tw-w-full tw-text-left"
                onClick={() => onChangeTrackingStatus(TrackingStatus.OVERDUE)}
            >
                <div className="card-header">
                    <div>
                        <h2 className="fw-bolder mb-0">{changeRequestDashboard?.overdue}</h2>
                        <p className="card-text">Overdue</p>
                    </div>
                    <div className="avatar bg-light-warning p-50 m-0">
                        <div className="avatar-content">
                            <i className="font-medium-5 bi bi-check-circle"></i>
                        </div>
                    </div>
                </div>
            </button>
            <button
                type="button"
                className="card tw-w-full tw-text-left"
                onClick={() => onChangeTrackingStatus(TrackingStatus.BACKLOG)}
            >
                <div className="card-header">
                    <div>
                        <h2 className="fw-bolder mb-0">{changeRequestDashboard?.backlog}</h2>
                        <p className="card-text">Backlog</p>
                    </div>
                    <div className="avatar bg-light-primary p-50 m-0">
                        <div className="avatar-content">
                            <i className="font-medium-5 bi bi-check-circle"></i>
                        </div>
                    </div>
                </div>
            </button>
        </div>
    );
}
