import ContentHeader from 'components/partials/ContentHeader';
import PaginationTable from 'components/partials/PaginationTable';
import Spinner from 'components/partials/Spinner';
import { PAGINATION, QUERY_KEY } from 'constants/common';
import useQueryParams from 'hooks/useQueryParams';
import isUndefined from 'lodash/isUndefined';
import omitBy from 'lodash/omitBy';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import { generateFilters } from 'utils/common';
import { useGraphQLQuery } from 'hooks/useGraphQLQuery';
import {
    ChangeRequestDashboard,
    ChangeRequestQueryRes,
    SearchWorkflowInstanceParam,
    workflowInstanceFilterConfig,
    WorkflowListQueryVariables,
} from 'types/Workflow';
import { CHANGE_REQUEST_DASHBOARD, CHANGE_REQUEST_LIST } from 'services/WorkflowService';
import { keepPreviousData } from '@tanstack/react-query';
import ListChangeRequest from '../components/ListChangeRequest';
import { convertDateRangeToQueryParams } from 'utils/date';
import SearchWorkflowInstanceForm from 'features/workflow/components/SearchWorkflowInstanceForm';
import ChangeRequestStatistical from '../components/ChangeRequestStatistical';

export default function ChangeRequest() {
    const { t } = useTranslation();

    const { queryParams, setQueryParams } = useQueryParams<SearchWorkflowInstanceParam>();
    const paramConfig: SearchWorkflowInstanceParam = omitBy(
        {
            limit: queryParams.limit ?? PAGINATION.limit,
            page: queryParams.page ?? '1',
            search: queryParams.search,
            status_id: queryParams.status_id,
            created_at_from:
                convertDateRangeToQueryParams(queryParams.created_at__range || '')?.created_at_from ?? undefined,
            created_at_to:
                convertDateRangeToQueryParams(queryParams.created_at__range || '')?.created_at_to ?? undefined,
            current_status: queryParams['current_status'],
            type: queryParams.type,
            tracking_status: queryParams.tracking_status,
        },
        isUndefined
    );

    const { limit, search, page, ...dataParamConfig } = paramConfig;
    const filters = generateFilters(dataParamConfig, workflowInstanceFilterConfig);

    const updatedFilters = filters.map((filter) =>
        filter.startsWith('current_status:') ? filter.replace(/^current_status:/, 'current_user_task.status:') : filter
    );

    const { data, isRefetching } = useGraphQLQuery<ChangeRequestQueryRes, WorkflowListQueryVariables>(
        [QUERY_KEY.WORKFLOWS_INSTANCE, queryParams],
        CHANGE_REQUEST_LIST,
        {
            page: Number(page),
            limit: Number(limit),
            sort: 'created_at:DESC',
            search: search,
            filters: filters.length > 0 ? updatedFilters : [],
        },
        '',
        {
            placeholderData: keepPreviousData,
        }
    );

    const handlePageChange = (_event: React.ChangeEvent<unknown>, page: number) => {
        setQueryParams({
            ...paramConfig,
            page: page.toString(),
        });
    };

    const { data: changeRequestDashboard } = useGraphQLQuery<{
        workflow_instances_auth_dashboard: ChangeRequestDashboard;
    }>([QUERY_KEY.CHANGE_REQUEST_DASHBOARD], CHANGE_REQUEST_DASHBOARD, undefined, '', {
        placeholderData: keepPreviousData,
    });

    const handleSearchOnDashboard = (trackingStatus: string | null) => {
        setQueryParams({
            tracking_status: trackingStatus ?? '',
            page: '1',
        });
    };

    return (
        <>
            <Helmet>
                <title>{t('Change Request')}</title>
            </Helmet>
            <ContentHeader
                title={t('Change Request')}
                contextMenu={[
                    {
                        text: 'New Request',
                        to: '/changeRequest/add',
                        icon: 'PLUS',
                    },
                ]}
            />
            <div className="content-body">
                <div className="col-12">
                    <ChangeRequestStatistical
                        changeRequestDashboard={changeRequestDashboard?.workflow_instances_auth_dashboard}
                        onChangeTrackingStatus={handleSearchOnDashboard}
                        type="changeRequest"
                    />

                    <SearchWorkflowInstanceForm isLoading={isRefetching} pageType="changeRequest" />

                    {isRefetching && <Spinner />}

                    {!isRefetching && data && (
                        <div className="card">
                            <ListChangeRequest items={data?.change_request_list?.data} />
                            <PaginationTable
                                countItem={data?.change_request_list?.totalCount}
                                totalPage={data?.change_request_list?.totalPages}
                                currentPage={data?.change_request_list?.currentPage}
                                handlePageChange={handlePageChange}
                            />
                        </div>
                    )}
                </div>
            </div>
        </>
    );
}
