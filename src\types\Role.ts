import { BaseModelString } from './common';
import Area from './OperationalArea';
import { UserAccount } from './User';

export default interface Role extends BaseModelString {
    name: string;
    type: RoleType;
    is_gatekeeper_child: boolean;
    userAreaRoles: UserAreaRole[];
}

export interface UserAreaRole extends BaseModelString {
    user_id: string;
    area_id: string;
    role_id: string;
    create_at: string;
    user: UserAccount;
    area: Area;
    role: Role;
}

export enum RoleType {
    AREA_BASED = 1,
    SYSTEM_WIDE = 2,
}

export const RoleTypeNames = [
    { id: RoleType.AREA_BASED, name: 'Area Based' },
    { id: RoleType.SYSTEM_WIDE, name: 'System Wide' },
];

export interface RoleQueryRes {
    roles_list: Role[];
}
