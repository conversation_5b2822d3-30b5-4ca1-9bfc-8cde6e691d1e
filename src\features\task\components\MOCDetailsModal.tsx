import React, { useState, useRef } from 'react';
import classNames from 'classnames';
import { toggleModalOpen } from 'utils/common';
import TaskCommentsSection from './TaskCommentsSection';
import MOCDetailsSection from './MOCDetailsSection';
import TaskComment, { ITaskCommentGroup } from '../../../types/TaskComment';
import { Task, TaskFile, TaskStatus } from '../../../types/Task';
import { IFile } from '../../../types/common/Item';
import { UserTaskEntity } from 'types/Workflow';
import { useReactToPrint } from 'react-to-print';
import TaskAttachmentsSection from './TaskAttachmentsSection';

interface FileAttachment {
    workflow_step_id: string;
    workflow_step_name: string;
    workflow_step_order: number;
    files: (TaskFile | IFile)[];
}

interface IProps {
    show: boolean;
    changeShow: (show: boolean) => void;
    taskComments: TaskComment[];
    activeTab?: string;
    onSendComment?: (content: string) => void;
    onDeleteComment?: (id: string) => void;
    isSendingComment?: boolean;
    userTaskDetail?: Task;
    groupedTaskComments: ITaskCommentGroup[];
    fileAttachments: FileAttachment[];
    fileLength: number;
}

export default function MOCDetailsModal({
    show,
    changeShow,
    taskComments,
    activeTab = 'moc-details',
    onSendComment,
    onDeleteComment,
    isSendingComment,
    userTaskDetail,
    groupedTaskComments,
    fileAttachments,
    fileLength,
}: Readonly<IProps>) {
    const [currentTab, setCurrentTab] = useState(activeTab);
    const [isLoadingPrint, setIsLoadingPrint] = useState(false);

    React.useLayoutEffect(() => toggleModalOpen(show), [show]);
    React.useEffect(() => {
        setCurrentTab(activeTab);
    }, [activeTab]);

    const handleTabClick = (tabId: string) => {
        setCurrentTab(tabId);
    };

    // Create a separate ref for export content
    const exportContentRef = useRef<HTMLDivElement>(null);

    const handleExportPDF = useReactToPrint({
        contentRef: exportContentRef,
        documentTitle: `MOC-Details-${userTaskDetail?.workflow_instance?.business_key || 'export'}`,
        pageStyle: `
            @page {
                size: A4;
                margin: 15mm;
            }

            @media print {
                html, body {
                    height: auto !important;
                    max-height: none !important;
                    overflow: visible !important;
                }

                body {
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    font-size: 11px;
                    line-height: 1.3;
                    color: #000000 !important;
                    background: white !important;
                    margin: 0 !important;
                    padding: 0 !important;
                    -webkit-print-color-adjust: exact;
                    color-adjust: exact;
                }

                * {
                    color: #000000 !important;
                    background-color: transparent !important;
                    border-color: #cccccc !important;
                    box-shadow: none !important;
                }

                .tw-bg-white {
                    background-color: #ffffff !important;
                }

                .tw-bg-gray-50 {
                    background-color: #f9fafb !important;
                }

                .tw-text-gray-900, .tw-text-gray-800, .tw-text-gray-700 {
                    color: #1f2937 !important;
                }

                .tw-text-gray-600, .tw-text-gray-500 {
                    color: #6b7280 !important;
                }

                .tw-text-gray-400 {
                    color: #9ca3af !important;
                }

                .tw-border-gray-300 {
                    border-color: #d1d5db !important;
                }

                .tw-border-gray-200 {
                    border-color: #e5e7eb !important;
                }

                .card {
                    border: 1px solid #cccccc !important;
                    margin-bottom: 10px !important;
                    page-break-inside: avoid;
                    height: auto !important;
                    max-height: none !important;
                    overflow: visible !important;
                }

                .card-header {
                    background-color: #f8f8f8 !important;
                    border-bottom: 1px solid #cccccc !important;
                    padding: 8px 12px !important;
                }

                .card-body {
                    padding: 12px !important;
                    height: auto !important;
                    max-height: none !important;
                    overflow: visible !important;
                }

                .collapsible-content {
                    height: auto !important;
                    max-height: none !important;
                    opacity: 1 !important;
                    overflow: visible !important;
                    transition: none !important;
                }

                .collapsible-content.overflow-hidden {
                    overflow: visible !important;
                }

                .tw-print-container {
                    page-break-inside: auto;
                    break-inside: auto;
                }

                .no-print {
                    display: none !important;
                }

                /* Force show all content */
                .tab-content {
                    display: block !important;
                }

                /* Ensure form fields are visible */
                .tw-px-8, .tw-py-60 {
                    padding: 12px !important;
                }

                /* Make sure text is visible */
                input, textarea, select {
                    color: #000000 !important;
                    background: white !important;
                    border: 1px solid #cccccc !important;
                }

                /* Ensure labels are visible */
                label {
                    color: #000000 !important;
                    font-weight: bold !important;
                }

                /* Force all containers to show full content */
                .tw-px-8, .tw-py-60, .pb-0, .space-y-4 {
                    height: auto !important;
                    max-height: none !important;
                    overflow: visible !important;
                }

                /* Ensure all divs show their content */
                div {
                    height: auto !important;
                    max-height: none !important;
                    overflow: visible !important;
                }

                /* Remove any height restrictions from form containers */
                .form-group, .form-control, .input-group {
                    height: auto !important;
                    max-height: none !important;
                    overflow: visible !important;
                }

                /* Ensure print captures full content */
                .tw-print-container {
                    height: auto !important;
                    max-height: none !important;
                    overflow: visible !important;
                    page-break-inside: auto !important;
                }

                /* Force expand all content for print */
                * {
                    height: auto !important;
                    max-height: none !important;
                    overflow: visible !important;
                    page-break-inside: avoid !important;
                }
            }
        `,
        onBeforePrint: () => {
            setIsLoadingPrint(true);
            console.log('Debug - Export content ref:', exportContentRef.current);
            console.log('Debug - User task detail:', userTaskDetail);
            console.log('Debug - Grouped by task key:', groupedByTaskKey);
            console.log('Debug - Export content HTML:', exportContentRef.current?.innerHTML);
            console.log('Debug - Export content text:', exportContentRef.current?.textContent);

            // Force expand all collapsible forms and remove all restrictions
            const collapsibleContents = exportContentRef.current?.querySelectorAll('.collapsible-content');
            collapsibleContents?.forEach((content) => {
                const element = content as HTMLElement;
                element.style.height = 'auto';
                element.style.maxHeight = 'none';
                element.style.opacity = '1';
                element.style.overflow = 'visible';
                element.style.transition = 'none';
                element.classList.remove('overflow-hidden');

                // Also ensure the card body is fully visible
                const cardBody = element.querySelector('.card-body') as HTMLElement;
                if (cardBody) {
                    cardBody.style.height = 'auto';
                    cardBody.style.maxHeight = 'none';
                    cardBody.style.overflow = 'visible';
                }
            });

            // Force expand all cards
            const cards = exportContentRef.current?.querySelectorAll('.card');
            cards?.forEach((card) => {
                const element = card as HTMLElement;
                element.style.height = 'auto';
                element.style.maxHeight = 'none';
                element.style.overflow = 'visible';
            });

            return new Promise<void>((resolve) => {
                // Step 1: Force render all content first
                const forceRenderContent = () => {
                    if (!exportContentRef.current) return;

                    // Make export container temporarily visible to ensure full render
                    const exportContainer = exportContentRef.current;
                    const originalStyle = {
                        position: exportContainer.style.position,
                        left: exportContainer.style.left,
                        visibility: exportContainer.style.visibility,
                    };

                    exportContainer.style.position = 'static';
                    exportContainer.style.left = '0';
                    exportContainer.style.visibility = 'visible';
                    exportContainer.style.height = 'auto';
                    exportContainer.style.maxHeight = 'none';
                    exportContainer.style.overflow = 'visible';

                    // Force scroll through entire content to trigger lazy loading
                    const scrollHeight = exportContainer.scrollHeight;
                    let currentScroll = 0;
                    const scrollStep = 100;

                    const scrollInterval = setInterval(() => {
                        currentScroll += scrollStep;
                        exportContainer.scrollTop = currentScroll;

                        if (currentScroll >= scrollHeight) {
                            clearInterval(scrollInterval);

                            // Reset scroll to top
                            exportContainer.scrollTop = 0;

                            // Restore original position (hidden)
                            exportContainer.style.position = originalStyle.position;
                            exportContainer.style.left = originalStyle.left;
                            exportContainer.style.visibility = originalStyle.visibility;

                            // Final processing
                            setTimeout(() => {
                                setIsLoadingPrint(false);
                                console.log('Debug - After full render, content height:', exportContainer.scrollHeight);
                                console.log(
                                    'Debug - After full render, content text length:',
                                    exportContainer.textContent?.length
                                );
                                resolve();
                            }, 500);
                        }
                    }, 50);
                };

                // Start the process after initial expansion
                setTimeout(forceRenderContent, 500);
            });
        },
        onAfterPrint: () => {
            console.log('Export completed successfully!');
        },
    });

    const groupedByTaskKey: Record<string, UserTaskEntity[]> = (userTaskDetail?.workflow_instance?.user_tasks || [])
        .filter((task) => task.status !== TaskStatus.IN_PROGRESS)
        .reduce((acc, task) => {
            const key = task.task_key;
            if (!acc[key]) acc[key] = [];
            acc[key].push(task);
            return acc;
        }, {} as Record<string, UserTaskEntity[]>);

    const renderTabContent = () => {
        switch (currentTab) {
            case 'moc-details':
                return (
                    <MOCDetailsSection
                        userTaskDetail={userTaskDetail}
                        groupedByTaskKey={groupedByTaskKey}
                        forExport={false}
                    />
                );
            case 'comments':
                return (
                    <TaskCommentsSection
                        taskComments={taskComments}
                        onSendComment={onSendComment}
                        onDeleteComment={onDeleteComment}
                        isSendingComment={isSendingComment}
                        userTaskDetail={userTaskDetail}
                        groupedTaskComments={groupedTaskComments}
                    />
                );
            case 'attachments':
                return <TaskAttachmentsSection fileAttachments={fileAttachments} />;
            default:
                return null;
        }
    };

    return (
        <>
            <div
                className={classNames('modal fade text-start modal-primary', { show })}
                style={{ display: show ? 'block' : 'none' }}
            >
                <div className="modal-dialog modal-dialog-centered modal-dialog-scrollable tw-max-w-[1200px]">
                    <div className="modal-content">
                        <div className="modal-header">
                            <h5 className="modal-title">
                                MOC Details: {userTaskDetail?.workflow_instance?.business_key}
                            </h5>
                            <button type="button" className="btn-close" onClick={() => changeShow(false)} />
                        </div>
                        <div className="modal-body">
                            {/* Tabs */}
                            <div className="tw-flex tw-items-center tw-justify-between tw-mb-2">
                                <ul className="nav nav-tabs mb-2" role="tablist">
                                    <li className="nav-item">
                                        <button
                                            className={classNames('nav-link cursor-pointer', {
                                                active: currentTab === 'moc-details',
                                            })}
                                            onClick={() => handleTabClick('moc-details')}
                                            role="tab"
                                            aria-selected={currentTab === 'moc-details'}
                                        >
                                            MOC Details
                                        </button>
                                    </li>
                                    <li className="nav-item">
                                        <button
                                            className={classNames('nav-link cursor-pointer', {
                                                active: currentTab === 'comments',
                                            })}
                                            onClick={() => handleTabClick('comments')}
                                            role="tab"
                                            aria-selected={currentTab === 'comments'}
                                        >
                                            Comments ({taskComments?.length})
                                        </button>
                                    </li>
                                    <li className="nav-item">
                                        <button
                                            className={classNames('nav-link cursor-pointer', {
                                                active: currentTab === 'attachments',
                                            })}
                                            onClick={() => handleTabClick('attachments')}
                                            role="tab"
                                            aria-selected={currentTab === 'attachments'}
                                        >
                                            Attachments ({fileLength})
                                        </button>
                                    </li>
                                </ul>
                                {/* <button
                                    type="button"
                                    className="btn btn-primary"
                                    onClick={handleExportPDF}
                                    disabled={isLoadingPrint}
                                >
                                    {isLoadingPrint ? 'Exporting...' : 'Export MOC Details'}
                                </button> */}
                            </div>

                            {/* Tab Content */}
                            <div className="tab-content">{renderTabContent()}</div>
                        </div>
                    </div>
                </div>
            </div>
            {show && <div className="modal-backdrop fade show" />}

            {/* Hidden content for export - ensure full content is rendered */}
            {/* <div
                ref={exportContentRef}
                style={{
                    position: 'absolute',
                    top: '0',
                    left: '0',
                    width: '210mm',
                    minHeight: '100vh',
                    maxHeight: 'none',
                    height: 'auto',
                    backgroundColor: '#ffffff',
                    overflow: 'visible',
                    zIndex: -1,
                    padding: '20px',
                }}
            >
                <div
                    className="tw-print-container"
                    style={{
                        height: 'auto',
                        maxHeight: 'none',
                        overflow: 'visible',
                        minHeight: 'fit-content',
                    }}
                >
                    <h2 style={{ marginBottom: '20px', textAlign: 'center' }}>
                        MOC Details: {userTaskDetail?.workflow_instance?.business_key}
                    </h2>
                    <div style={{ height: 'auto', maxHeight: 'none', overflow: 'visible' }}>
                        <MOCDetailsSection
                            userTaskDetail={userTaskDetail}
                            groupedByTaskKey={groupedByTaskKey}
                            forExport={true}
                        />
                    </div>
                </div>
            </div> */}
        </>
    );
}
