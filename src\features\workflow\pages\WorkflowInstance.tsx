import ContentHeader from 'components/partials/ContentHeader';
import PaginationTable from 'components/partials/PaginationTable';
import Spinner from 'components/partials/Spinner';
import { PAGINATION, QUERY_KEY } from 'constants/common';
import useQueryParams from 'hooks/useQueryParams';
import isUndefined from 'lodash/isUndefined';
import omitBy from 'lodash/omitBy';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import { generateFilters } from 'utils/common';
import ListWorkflowInstance from '../components/ListWorkflowInstance';
import SearchWorkflowInstanceForm from '../components/SearchWorkflowInstanceForm';
import { useGraphQLQuery } from 'hooks/useGraphQLQuery';
import {
    WorkflowListQueryVariables,
    WorkflowInstanceQuery,
    workflowInstanceFilterConfig,
    SearchWorkflowInstanceParam,
    ChangeRequestDashboard,
} from 'types/Workflow';
import { WORKFLOW_INSTANCE_DASHBOARD, WORKFLOW_INSTANCES_LIST } from 'services/WorkflowService';
import { keepPreviousData } from '@tanstack/react-query';
import { convertDateRangeToQueryParams } from '../../../utils/date';
import { AreaQueryRes, AreaType } from '../../../types/OperationalArea';
import { AREAS_LIST } from '../../../services/AreaService';
import ChangeRequestStatistical from '../../changeRequest/components/ChangeRequestStatistical';

export default function WorkflowInstance() {
    const { t } = useTranslation();

    const { queryParams, setQueryParams } = useQueryParams<SearchWorkflowInstanceParam>();
    const paramConfig: SearchWorkflowInstanceParam = omitBy(
        {
            limit: queryParams.limit ?? PAGINATION.limit,
            page: queryParams.page ?? '1',
            search: queryParams.search,
            status_id: queryParams.status_id,
            created_at_from:
                convertDateRangeToQueryParams(queryParams.created_at__range || '')?.created_at_from ?? undefined,
            created_at_to:
                convertDateRangeToQueryParams(queryParams.created_at__range || '')?.created_at_to ?? undefined,
            current_status: queryParams['current_status'],
            type: queryParams.type,
            sub_area_id: queryParams.sub_area_id,
            tracking_status: queryParams.tracking_status,
            sort: queryParams.sort,
        },
        isUndefined
    );

    const { limit, search, page, ...dataParamConfig } = paramConfig;
    const filters = generateFilters(dataParamConfig, workflowInstanceFilterConfig);

    const updatedFilters = filters.map((filter) =>
        filter.startsWith('current_status:') ? filter.replace(/^current_status:/, 'current_user_task.status:') : filter
    );
    const { data, isRefetching } = useGraphQLQuery<WorkflowInstanceQuery, WorkflowListQueryVariables>(
        [QUERY_KEY.WORKFLOWS_INSTANCE, queryParams],
        WORKFLOW_INSTANCES_LIST,
        {
            page: Number(page),
            limit: Number(limit),
            sort: paramConfig.sort || '',
            search: search,
            filters: filters.length > 0 ? updatedFilters : [],
        },
        '',
        {
            placeholderData: keepPreviousData,
        }
    );

    const handlePageChange = (_event: React.ChangeEvent<unknown>, page: number) => {
        setQueryParams({
            ...paramConfig,
            page: page.toString(),
        });
    };

    const handleSort = (field: string) => {
        const currentSort = paramConfig.sort || '';
        let newSort = '';
        if (!currentSort) {
            newSort = `${field}:ASC`;
        } else if (currentSort === `${field}:ASC`) {
            newSort = `${field}:DESC`;
        } else if (currentSort === `${field}:DESC`) {
            newSort = `${field}:ASC`;
        }

        setQueryParams({
            ...paramConfig,
            sort: newSort,
            page: '1',
        });
    };

    const { data: subAreaData } = useGraphQLQuery<AreaQueryRes>(
        [QUERY_KEY.AREAS, AreaType.SUB_AREA],
        AREAS_LIST,
        {
            filters: [`type:=(${AreaType.SUB_AREA})`],
        },
        '',
        {
            placeholderData: keepPreviousData,
        }
    );

    const { data: workflowInstanceDashboard } = useGraphQLQuery<{
        workflow_instances_dashboard: ChangeRequestDashboard;
    }>([QUERY_KEY.WORKFLOW_INSTANCE_DASHBOARD], WORKFLOW_INSTANCE_DASHBOARD, undefined, '', {
        placeholderData: keepPreviousData,
    });

    const handleSearchOnDashboard = (trackingStatus: string | null) => {
        setQueryParams({
            tracking_status: trackingStatus ?? '',
            page: '1',
        });
    };

    return (
        <>
            <Helmet>
                <title>{t('Workflow Instance')}</title>
            </Helmet>
            <ContentHeader title={t('Workflow Instance')} />
            <div className="content-body">
                <div className="col-12">
                    <ChangeRequestStatistical
                        changeRequestDashboard={workflowInstanceDashboard?.workflow_instances_dashboard}
                        onChangeTrackingStatus={handleSearchOnDashboard}
                        type="workflowInstance"
                    />
                    <SearchWorkflowInstanceForm
                        isLoading={isRefetching}
                        pageType="workflowInstance"
                        subArea={subAreaData?.areas_list}
                    />
                    {isRefetching && <Spinner />}
                    {!isRefetching && data && (
                        <div className="card">
                            <ListWorkflowInstance
                                items={data?.workflow_instances_list?.data.filter((item) => item.status !== 'DRAFT')}
                                areas_list={subAreaData?.areas_list ?? []}
                                onSort={handleSort}
                                currentSort={paramConfig.sort || ''}
                            />
                            <PaginationTable
                                countItem={data.workflow_instances_list?.totalCount || 0}
                                totalPage={data.workflow_instances_list?.totalPages}
                                currentPage={data.workflow_instances_list?.currentPage}
                                handlePageChange={handlePageChange}
                            />
                        </div>
                    )}
                </div>
            </div>
        </>
    );
}
